import '../../../core/app_export.dart';

class ProfileModel extends Equatable {
  String? userName;
  String? userEmail;
  String? profileImage;

  ProfileModel({this.userName, this.userEmail, this.profileImage}) {
    userName = userName ?? '<PERSON> Doe';
    userEmail = userEmail ?? '<EMAIL>';
    profileImage = profileImage ?? ImageConstant.imgEllipse43;
  }

  @override
  List<Object?> get props => [userName, userEmail, profileImage];

  ProfileModel copyWith({String? userName, String? userEmail, String? profileImage}) {
    return ProfileModel(userName: userName ?? this.userName, userEmail: userEmail ?? this.userEmail, profileImage: profileImage ?? this.profileImage);
  }
}

// Modified: Added ProfileMenuItemModel class to resolve undefined class error
class ProfileMenuItemModel extends Equatable {
  final String? icon;
  final String? title;
  final String? subtitle;

  const ProfileMenuItemModel({this.icon, this.title, this.subtitle});

  @override
  List<Object?> get props => [icon, title, subtitle];
}
