import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/app_export.dart';
import 'l10n/app_localizations.dart';
import 'presentation/localization/bloc/localization_bloc.dart';
import 'presentation/theme/bloc/theme_bloc.dart';

var globalMessengerKey = GlobalKey<ScaffoldMessengerState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final prefs = await SharedPreferences.getInstance();

  Future.wait([
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]),
  ]).then((value) {
    runApp(MyApp(prefs: prefs));
  });
}

class MyApp extends StatelessWidget {
  final SharedPreferences prefs;

  const MyApp({super.key, required this.prefs});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ThemeBloc>(create: (context) => ThemeBloc(prefs)..add(LoadThemeEvent())),
        BlocProvider<LocalizationBloc>(create: (context) => LocalizationBloc(prefs)..add(LoadLanguageEvent())),
      ],
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return BlocBuilder<LocalizationBloc, LocalizationState>(
            builder: (context, localeState) {
              // Update the theme helper with current theme state
              // This ensures theme is updated even after language changes
              themeHelperInstance.updateThemeMode(themeState.isDarkMode);
              return Sizer(
                builder: (context, orientation, deviceType) {
                  return MaterialApp(
                    title: 'authentication_app',
                    themeMode: themeState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
                    theme: lightTheme, // from your helper
                    darkTheme: darkTheme,
                    builder: (context, child) {
                      return Directionality(
                        textDirection: localeState.locale.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr,
                        child: MediaQuery(
                          data: MediaQuery.of(context).copyWith(textScaler: TextScaler.linear(1.0)),
                          child: child!,
                        ),
                      );
                    },
                    navigatorKey: NavigatorService.navigatorKey,
                    debugShowCheckedModeBanner: false,
                    localizationsDelegates: [
                      AppLocalizations.delegate,
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate,
                    ],
                    supportedLocales: [Locale('en', ''), Locale('ar', '')],
                    locale: localeState.locale,
                    initialRoute: AppRoutes.initialRoute,
                    routes: AppRoutes.routes,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
