import 'package:flutter/material.dart';

import '../core/app_export.dart';

/// A helper class for managing text styles in the application
class TextStyleHelper {
  static TextStyleHelper? _instance;

  TextStyleHelper._();

  static TextStyleHelper get instance {
    _instance ??= TextStyleHelper._();
    return _instance!;
  }

  // Display Styles
  // Large text styles for prominent headings

  TextStyle get display36SemiBold => TextStyle(fontSize: 36.fSize, fontWeight: FontWeight.w600, color: appTheme.colorFF1616);

  TextStyle get display30SemiBold => TextStyle(fontSize: 30.fSize, fontWeight: FontWeight.w600, color: appTheme.colorFF1616);

  // Headline Styles
  // Medium-large text styles for section headers

  TextStyle get headline24SemiBold => TextStyle(fontSize: 24.fSize, fontWeight: FontWeight.w600, color: appTheme.colorFF1616);

  TextStyle get headline20SemiBold => TextStyle(fontSize: 20.fSize, fontWeight: FontWeight.w600, color: appTheme.colorFF1616);

  TextStyle get headline18Medium => TextStyle(fontSize: 18.fSize, fontWeight: FontWeight.w500, color: appTheme.colorFF1616);

  TextStyle get headline18 => TextStyle(fontSize: 18.fSize, color: appTheme.colorFF1616);

  // Title Styles
  // Medium text styles for titles and subtitles

  TextStyle get title20RegularRoboto => TextStyle(fontSize: 20.fSize, fontWeight: FontWeight.w400, fontFamily: 'Roboto');

  TextStyle get title18MediumPoppins =>
      TextStyle(fontSize: 18.fSize, fontWeight: FontWeight.w500, fontFamily: 'Poppins', color: appTheme.colorFF1616);

  TextStyle get title16Medium => TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w500, color: appTheme.colorFF1616);

  TextStyle get title16Regular => TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w400, color: appTheme.colorFF5252);

  TextStyle get title16RegularPoppins =>
      TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w400, fontFamily: 'Poppins', color: appTheme.colorFF1616);

  TextStyle get title16 => TextStyle(fontSize: 16.fSize, color: appTheme.colorFF1616);

  // Body Styles
  // Standard text styles for body content

  TextStyle get body14Regular => TextStyle(fontSize: 14.fSize, fontWeight: FontWeight.w400, color: appTheme.colorFF5252);

  TextStyle get body14Medium => TextStyle(fontSize: 14.fSize, fontWeight: FontWeight.w500, color: appTheme.colorFF1616);

  TextStyle get body14SemiBold => TextStyle(fontSize: 14.fSize, fontWeight: FontWeight.w600, color: appTheme.colorFF1616);

  TextStyle get body14 => TextStyle(fontSize: 14.fSize, color: appTheme.colorFF1616);

  TextStyle get body12Medium => TextStyle(fontSize: 12.fSize, fontWeight: FontWeight.w500, color: appTheme.colorFF1616);

  TextStyle get body12MediumPoppins => TextStyle(fontSize: 12.fSize, fontWeight: FontWeight.w500, fontFamily: 'Poppins', color: appTheme.colorFF1616);

  TextStyle get body12MediumInter => TextStyle(fontSize: 12.fSize, fontWeight: FontWeight.w500, fontFamily: 'Inter', color: appTheme.colorFF1616);

  TextStyle get body12SemiBold => TextStyle(fontSize: 12.fSize, fontWeight: FontWeight.w600, color: appTheme.colorFF7E56);

  TextStyle get body12Regular => TextStyle(fontSize: 12.fSize, fontWeight: FontWeight.w400, color: appTheme.colorFF5252);

  TextStyle get body12 => TextStyle(fontSize: 12.fSize, color: appTheme.colorFF5252);
}
