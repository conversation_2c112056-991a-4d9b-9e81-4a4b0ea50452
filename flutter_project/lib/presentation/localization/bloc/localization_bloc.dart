import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/app_export.dart';

part 'localization_event.dart';
part 'localization_state.dart';

class LocalizationBloc extends Bloc<LocalizationEvent, LocalizationState> {
  final SharedPreferences prefs;
  static const String _languageKey = 'selectedLanguage';

  LocalizationBloc(this.prefs) : super(const LocalizationState()) {
    on<LoadLanguageEvent>(_onLoadLanguage);
    on<ChangeLanguageEvent>(_onChangeLanguage);
  }

  Future<void> _onLoadLanguage(LoadLanguageEvent event, Emitter<LocalizationState> emit) async {
    final languageCode = prefs.getString(_languageKey) ?? 'en';
    emit(state.copyWith(locale: Locale(languageCode)));
  }

  Future<void> _onChangeLanguage(ChangeLanguageEvent event, Emitter<LocalizationState> emit) async {
    await prefs.setString(_languageKey, event.locale.languageCode);
    emit(state.copyWith(locale: event.locale));
  }
}
