part of 'landing_bloc.dart';

abstract class Landing<PERSON>vent extends Equatable {
  const LandingEvent();

  @override
  List<Object?> get props => [];
}

class LandingInitialEvent extends LandingEvent {}

class MenuButtonPressedEvent extends LandingEvent {}

class AppStoreButtonPressedEvent extends LandingEvent {}

class PlayStoreButtonPressedEvent extends LandingEvent {}

class CtaAppStoreButtonPressedEvent extends LandingEvent {}

class CtaPlayStoreButtonPressedEvent extends LandingEvent {}

class PricingPlanSelectedEvent extends LandingEvent {
  final PricingPlanModel plan;

  const PricingPlanSelectedEvent(this.plan);

  @override
  List<Object?> get props => [plan];
}

class EmailChangedEvent extends LandingEvent {
  final String email;

  const EmailChangedEvent(this.email);

  @override
  List<Object?> get props => [email];
}

class SubscribeButtonPressedEvent extends LandingEvent {}

class BottomNavItemTappedEvent extends LandingEvent {
  final String item;

  const BottomNavItemTappedEvent(this.item);

  @override
  List<Object?> get props => [item];
}
