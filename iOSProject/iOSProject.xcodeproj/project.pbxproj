// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		1823FC062E45F7A500CA007E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1823FBF02E45F7A300CA007E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1823FBF72E45F7A300CA007E;
			remoteInfo = iOSProject;
		};
		1823FC102E45F7A500CA007E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1823FBF02E45F7A300CA007E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1823FBF72E45F7A300CA007E;
			remoteInfo = iOSProject;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1823FBF82E45F7A300CA007E /* iOSProject.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iOSProject.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1823FC052E45F7A500CA007E /* iOSProjectTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSProjectTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1823FC0F2E45F7A500CA007E /* iOSProjectUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSProjectUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1823FBFA2E45F7A300CA007E /* iOSProject */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSProject;
			sourceTree = "<group>";
		};
		1823FC082E45F7A500CA007E /* iOSProjectTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSProjectTests;
			sourceTree = "<group>";
		};
		1823FC122E45F7A500CA007E /* iOSProjectUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSProjectUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1823FBF52E45F7A300CA007E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1823FC022E45F7A500CA007E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1823FC0C2E45F7A500CA007E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1823FBEF2E45F7A300CA007E = {
			isa = PBXGroup;
			children = (
				1823FBFA2E45F7A300CA007E /* iOSProject */,
				1823FC082E45F7A500CA007E /* iOSProjectTests */,
				1823FC122E45F7A500CA007E /* iOSProjectUITests */,
				1823FBF92E45F7A300CA007E /* Products */,
			);
			sourceTree = "<group>";
		};
		1823FBF92E45F7A300CA007E /* Products */ = {
			isa = PBXGroup;
			children = (
				1823FBF82E45F7A300CA007E /* iOSProject.app */,
				1823FC052E45F7A500CA007E /* iOSProjectTests.xctest */,
				1823FC0F2E45F7A500CA007E /* iOSProjectUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1823FBF72E45F7A300CA007E /* iOSProject */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1823FC192E45F7A500CA007E /* Build configuration list for PBXNativeTarget "iOSProject" */;
			buildPhases = (
				1823FBF42E45F7A300CA007E /* Sources */,
				1823FBF52E45F7A300CA007E /* Frameworks */,
				1823FBF62E45F7A300CA007E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1823FBFA2E45F7A300CA007E /* iOSProject */,
			);
			name = iOSProject;
			packageProductDependencies = (
			);
			productName = iOSProject;
			productReference = 1823FBF82E45F7A300CA007E /* iOSProject.app */;
			productType = "com.apple.product-type.application";
		};
		1823FC042E45F7A500CA007E /* iOSProjectTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1823FC1C2E45F7A500CA007E /* Build configuration list for PBXNativeTarget "iOSProjectTests" */;
			buildPhases = (
				1823FC012E45F7A500CA007E /* Sources */,
				1823FC022E45F7A500CA007E /* Frameworks */,
				1823FC032E45F7A500CA007E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1823FC072E45F7A500CA007E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1823FC082E45F7A500CA007E /* iOSProjectTests */,
			);
			name = iOSProjectTests;
			packageProductDependencies = (
			);
			productName = iOSProjectTests;
			productReference = 1823FC052E45F7A500CA007E /* iOSProjectTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1823FC0E2E45F7A500CA007E /* iOSProjectUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1823FC1F2E45F7A500CA007E /* Build configuration list for PBXNativeTarget "iOSProjectUITests" */;
			buildPhases = (
				1823FC0B2E45F7A500CA007E /* Sources */,
				1823FC0C2E45F7A500CA007E /* Frameworks */,
				1823FC0D2E45F7A500CA007E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1823FC112E45F7A500CA007E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1823FC122E45F7A500CA007E /* iOSProjectUITests */,
			);
			name = iOSProjectUITests;
			packageProductDependencies = (
			);
			productName = iOSProjectUITests;
			productReference = 1823FC0F2E45F7A500CA007E /* iOSProjectUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1823FBF02E45F7A300CA007E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					1823FBF72E45F7A300CA007E = {
						CreatedOnToolsVersion = 16.4;
					};
					1823FC042E45F7A500CA007E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 1823FBF72E45F7A300CA007E;
					};
					1823FC0E2E45F7A500CA007E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 1823FBF72E45F7A300CA007E;
					};
				};
			};
			buildConfigurationList = 1823FBF32E45F7A300CA007E /* Build configuration list for PBXProject "iOSProject" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1823FBEF2E45F7A300CA007E;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1823FBF92E45F7A300CA007E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1823FBF72E45F7A300CA007E /* iOSProject */,
				1823FC042E45F7A500CA007E /* iOSProjectTests */,
				1823FC0E2E45F7A500CA007E /* iOSProjectUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1823FBF62E45F7A300CA007E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1823FC032E45F7A500CA007E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1823FC0D2E45F7A500CA007E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1823FBF42E45F7A300CA007E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1823FC012E45F7A500CA007E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1823FC0B2E45F7A500CA007E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1823FC072E45F7A500CA007E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1823FBF72E45F7A300CA007E /* iOSProject */;
			targetProxy = 1823FC062E45F7A500CA007E /* PBXContainerItemProxy */;
		};
		1823FC112E45F7A500CA007E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1823FBF72E45F7A300CA007E /* iOSProject */;
			targetProxy = 1823FC102E45F7A500CA007E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1823FC172E45F7A500CA007E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1823FC182E45F7A500CA007E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1823FC1A2E45F7A500CA007E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.demo.app.iOSProject;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1823FC1B2E45F7A500CA007E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.demo.app.iOSProject;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1823FC1D2E45F7A500CA007E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.demo.app.iOSProjectTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSProject.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSProject";
			};
			name = Debug;
		};
		1823FC1E2E45F7A500CA007E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.demo.app.iOSProjectTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSProject.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSProject";
			};
			name = Release;
		};
		1823FC202E45F7A500CA007E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.demo.app.iOSProjectUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOSProject;
			};
			name = Debug;
		};
		1823FC212E45F7A500CA007E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.demo.app.iOSProjectUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOSProject;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1823FBF32E45F7A300CA007E /* Build configuration list for PBXProject "iOSProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1823FC172E45F7A500CA007E /* Debug */,
				1823FC182E45F7A500CA007E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1823FC192E45F7A500CA007E /* Build configuration list for PBXNativeTarget "iOSProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1823FC1A2E45F7A500CA007E /* Debug */,
				1823FC1B2E45F7A500CA007E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1823FC1C2E45F7A500CA007E /* Build configuration list for PBXNativeTarget "iOSProjectTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1823FC1D2E45F7A500CA007E /* Debug */,
				1823FC1E2E45F7A500CA007E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1823FC1F2E45F7A500CA007E /* Build configuration list for PBXNativeTarget "iOSProjectUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1823FC202E45F7A500CA007E /* Debug */,
				1823FC212E45F7A500CA007E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1823FBF02E45F7A300CA007E /* Project object */;
}
