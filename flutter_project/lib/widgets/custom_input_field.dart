import 'package:flutter/material.dart';

import '../core/app_export.dart';

// CustomInputField - A reusable input field
class CustomInputField extends StatelessWidget {
  const CustomInputField({
    super.key,
    this.hintText,
    this.controller,
    this.keyboardType,
    this.validator,
    this.obscureText,
    this.enabled,
    this.maxLines,
    this.onChanged,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
  });

  /// Placeholder text to display when field is empty
  final String? hintText;

  /// TextEditingController to manage the input text
  final TextEditingController? controller;

  /// Type of keyboard to display (email, text, number, etc.)
  final TextInputType? keyboardType;

  /// Function to validate the input text
  final String? Function(String?)? validator;

  /// Whether to hide the input text (for passwords)
  final bool? obscureText;

  /// Whether the input field is enabled or disabled
  final bool? enabled;

  /// Maximum number of lines for the input field
  final int? maxLines;

  /// Callback function when text changes
  final Function(String)? onChanged;

  /// Callback function when field is tapped
  final VoidCallback? onTap;

  /// Widget to display at the beginning of the field
  final Widget? prefixIcon;

  /// Widget to display at the end of the field
  final Widget? suffixIcon;

  @override
  Widget build(BuildContext context) {
    // Adjust vertical padding when prefixIcon is present to ensure proper text alignment
    final bool hasPrefixIcon = prefixIcon != null;
    final double verticalPadding = maxLines != null && maxLines! > 1
        ? 12.h
        : hasPrefixIcon
        ? 12
              .h // Add vertical padding when prefixIcon is present
        : 0;

    return Container(
      height: maxLines != null && maxLines! > 1 ? null : 48.h,
      decoration: BoxDecoration(
        border: Border.all(color: appTheme.colorFFE0E0, width: 1),
        borderRadius: BorderRadius.circular(8.h),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType ?? TextInputType.text,
        validator: validator,
        obscureText: obscureText ?? false,
        enabled: enabled ?? true,
        maxLines: maxLines ?? 1,
        onChanged: onChanged,
        onTap: onTap,
        style: TextStyleHelper.instance.title16Regular,
        decoration: InputDecoration(
          hintText: hintText ?? "Enter text",
          hintStyle: TextStyleHelper.instance.title16Regular,
          prefixIcon: hasPrefixIcon
              ? Container(
                  width: 32.h, // Reduced from 48.h to bring icon closer to text
                  height: 48.h,
                  margin: EdgeInsets.only(left: 16.h),
                  alignment: Alignment.center,
                  child: prefixIcon,
                )
              : null,
          prefixIconConstraints: hasPrefixIcon
              ? BoxConstraints(
                  minWidth: 32.h, // Constrain the prefix icon area
                  maxWidth: 32.h,
                  minHeight: 48.h,
                  maxHeight: 48.h,
                )
              : null,
          suffixIcon: suffixIcon,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.h, // Reduce horizontal padding when prefixIcon is present
            vertical: verticalPadding,
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
        ),
      ),
    );
  }
}
