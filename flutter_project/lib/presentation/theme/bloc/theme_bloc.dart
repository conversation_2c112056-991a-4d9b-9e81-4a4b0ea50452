import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/app_export.dart';

part 'theme_event.dart';
part 'theme_state.dart';

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final SharedPreferences prefs;
  static const String _themeKey = 'isDarkMode';

  ThemeBloc(this.prefs) : super(const ThemeState()) {
    on<LoadThemeEvent>(_onLoadTheme);
    on<ToggleThemeEvent>(_onToggleTheme);
    on<SetThemeEvent>(_onSetTheme);
  }

  Future<void> _onLoadTheme(LoadThemeEvent event, Emitter<ThemeState> emit) async {
    final isDarkMode = prefs.getBool(_themeKey) ?? false;
    emit(state.copyWith(isDarkMode: isDarkMode));
  }

  Future<void> _onToggleTheme(ToggleThemeEvent event, Emitter<ThemeState> emit) async {
    final newTheme = !state.isDarkMode;
    await prefs.setBool(_themeKey, newTheme);
    emit(state.copyWith(isDarkMode: newTheme));
  }

  Future<void> _onSetTheme(SetThemeEvent event, Emitter<ThemeState> emit) async {
    await prefs.setBool(_themeKey, event.isDarkMode);
    emit(state.copyWith(isDarkMode: event.isDarkMode));
  }
}
