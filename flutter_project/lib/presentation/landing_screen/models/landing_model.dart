import '../../../core/app_export.dart';
import './feature_item_model.dart';
import './pricing_plan_model.dart';

class LandingModel extends Equatable {
  const LandingModel({this.features = const [], this.pricingPlans = const []});

  final List<FeatureItemModel> features;
  final List<PricingPlanModel> pricingPlans;

  LandingModel copyWith({List<FeatureItemModel>? features, List<PricingPlanModel>? pricingPlans}) {
    return LandingModel(features: features ?? this.features, pricingPlans: pricingPlans ?? this.pricingPlans);
  }

  @override
  List<Object?> get props => [features, pricingPlans];
}
