import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_image_view.dart';

class FeatureItemWidget extends StatelessWidget {
  final String icon;
  final String title;
  final String description;

  const FeatureItemWidget({super.key, required this.icon, required this.title, required this.description});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 40.h,
          width: 40.h,
          padding: EdgeInsets.all(8.h),
          decoration: BoxDecoration(
            color: appTheme.colorFFF4EBFF,
            border: Border.all(color: appTheme.colorFFF9F5FF, width: 6.h),
            borderRadius: BorderRadius.circular(20.h),
          ),
          child: CustomImageView(imagePath: icon, height: 24.h, width: 24.h),
        ),
        SizedBox(height: 16.h),
        Text(title, style: TextStyleHelper.instance.headline18Medium.copyWith(color: appTheme.colorFF0F1728)),
        SizedBox(height: 8.h),
        Text(description, style: TextStyleHelper.instance.title16.copyWith(color: appTheme.colorFF667084, height: 1.5)),
      ],
    );
  }
}
