import 'package:flutter/material.dart';
import 'package:flutter_project/widgets/custom_appbar.dart';

import '../../core/app_export.dart';
import './bloc/favourite_bloc.dart';
import './models/favourite_model.dart';

class FavoriteScreen extends StatelessWidget {
  const FavoriteScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<FavoriteBloc>(
      create: (context) => FavoriteBloc(FavoriteState(favoriteModel: FavoriteModel()))..add(FavouriteInitialEvent()),
      child: const FavoriteScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FavoriteBloc, FavoriteState>(
      builder: (context, state) {
        return Scaffold(
          appBar: CustomAppBar(),
          backgroundColor: Theme.of(context).colorScheme.surface,
          body: Center(child: Text('Favourite Screen', style: TextStyleHelper.instance.headline24SemiBold)),
        );
      },
    );
  }
}
