import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../models/authentication_model.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc(super.initialState) {
    on<AuthenticationInitialEvent>(_onInitialize);
    on<EmailChangedEvent>(_onEmailChanged);
    on<PasswordChangedEvent>(_onPasswordChanged);
    on<TogglePasswordVisibilityEvent>(_onTogglePasswordVisibility);
    on<LoginButtonTappedEvent>(_onLoginButtonTapped);
    on<GoogleLoginTappedEvent>(_onGoogleLoginTapped);
    on<AppleLoginTappedEvent>(_onAppleLoginTapped);
    on<ForgotPasswordTappedEvent>(_onForgotPasswordTapped);
    on<SignUpTappedEvent>(_onSignUpTapped);
  }

  _onInitialize(AuthenticationInitialEvent event, Emitter<AuthenticationState> emit) async {
    emit(
      state.copyWith(
        emailController: TextEditingController(),
        passwordController: TextEditingController(),
        isPasswordVisible: false,
        isLoading: false,
        isLoginSuccess: false,
        showError: false,
        errorMessage: '',
      ),
    );
  }

  _onEmailChanged(EmailChangedEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(email: event.email, showError: false));
  }

  _onPasswordChanged(PasswordChangedEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(password: event.password, showError: false));
  }

  _onTogglePasswordVisibility(TogglePasswordVisibilityEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(isPasswordVisible: !(state.isPasswordVisible ?? false)));
  }

  _onLoginButtonTapped(LoginButtonTappedEvent event, Emitter<AuthenticationState> emit) async {
    final email = state.emailController?.text ?? '';
    final password = state.passwordController?.text ?? '';

    if (email.isEmpty || password.isEmpty) {
      emit(state.copyWith(showError: true, errorMessage: 'Please fill in all fields'));
      return;
    }

    emit(state.copyWith(isLoading: true));

    // Simulate login process
    await Future.delayed(Duration(seconds: 1));

    if (email.contains('@') && password.length >= 6) {
      emit(state.copyWith(isLoading: false, isLoginSuccess: true, showError: false));
    } else {
      emit(state.copyWith(isLoading: false, showError: true, errorMessage: 'Invalid email or password'));
    }
  }

  _onGoogleLoginTapped(GoogleLoginTappedEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(isLoading: true));

    // Simulate Google login process
    await Future.delayed(Duration(seconds: 1));

    emit(state.copyWith(isLoading: false, isLoginSuccess: true));
  }

  _onAppleLoginTapped(AppleLoginTappedEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(isLoading: true));

    // Simulate Apple login process
    await Future.delayed(Duration(seconds: 1));

    emit(state.copyWith(isLoading: false, isLoginSuccess: true));
  }

  _onForgotPasswordTapped(ForgotPasswordTappedEvent event, Emitter<AuthenticationState> emit) async {
    // Handle forgot password logic
    // Navigate to forgot password screen or show dialog
  }

  _onSignUpTapped(SignUpTappedEvent event, Emitter<AuthenticationState> emit) async {
    // Handle sign up navigation
    // Navigate to sign up screen
  }
}
