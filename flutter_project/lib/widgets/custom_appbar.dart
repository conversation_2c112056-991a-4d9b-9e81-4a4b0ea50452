import 'package:flutter/material.dart';
import 'package:flutter_project/core/app_export.dart';
import 'package:flutter_project/presentation/main_navigation/bloc/drawer_bloc.dart';
import 'package:flutter_project/widgets/custom_image_view.dart';

final GlobalKey<ScaffoldState> appDrawerScaffoldKey = GlobalKey<ScaffoldState>();

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      surfaceTintColor: Colors.transparent,
      shadowColor: Theme.of(context).colorScheme.shadow,
      backgroundColor: Theme.of(context).colorScheme.surface,
      centerTitle: false, // Material 2 usually doesn't center titles by default
      iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
      leading: Padding(
        padding: const EdgeInsets.only(left: 16.0),
        child: CustomImageView(imagePath: ImageConstant.imgLogomark, height: 32.h, width: 32.h),
      ),
      leadingWidth: 52,
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: IconButton(
            onPressed: () {
              // Try to find DrawerBloc and open drawer
              try {
                context.read<DrawerBloc>().add(ToggleDrawerEvent());
                // Scaffold.of(context).openEndDrawer();
                appDrawerScaffoldKey.currentState?.openEndDrawer();
              } catch (e) {
                // DrawerBloc not available in this context, do nothing
              }
            },
            icon: CustomImageView(imagePath: ImageConstant.imgMenu, height: 24.h, width: 24.h, fit: BoxFit.contain),
          ),
        ),
      ],
    );
  }
}
