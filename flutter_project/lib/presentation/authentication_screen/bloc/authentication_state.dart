part of 'authentication_bloc.dart';

class AuthenticationState extends Equatable {
  final TextEditingController? emailController;
  final TextEditingController? passwordController;
  final String? email;
  final String? password;
  final bool? isPasswordVisible;
  final bool? isLoading;
  final bool? isLoginSuccess;
  final bool? showError;
  final String? errorMessage;
  final AuthenticationModel? authenticationModel;

  const AuthenticationState({
    this.emailController,
    this.passwordController,
    this.email,
    this.password,
    this.isPasswordVisible,
    this.isLoading,
    this.isLoginSuccess,
    this.showError,
    this.errorMessage,
    this.authenticationModel,
  });

  @override
  List<Object?> get props => [
        emailController,
        passwordController,
        email,
        password,
        isPasswordVisible,
        isLoading,
        isLoginSuccess,
        showError,
        errorMessage,
        authenticationModel,
      ];

  AuthenticationState copyWith({
    TextEditingController? emailController,
    TextEditingController? passwordController,
    String? email,
    String? password,
    bool? isPasswordVisible,
    bool? isLoading,
    bool? isLoginSuccess,
    bool? showError,
    String? errorMessage,
    AuthenticationModel? authenticationModel,
  }) {
    return AuthenticationState(
      emailController: emailController ?? this.emailController,
      passwordController: passwordController ?? this.passwordController,
      email: email ?? this.email,
      password: password ?? this.password,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isLoading: isLoading ?? this.isLoading,
      isLoginSuccess: isLoginSuccess ?? this.isLoginSuccess,
      showError: showError ?? this.showError,
      errorMessage: errorMessage ?? this.errorMessage,
      authenticationModel: authenticationModel ?? this.authenticationModel,
    );
  }
}
