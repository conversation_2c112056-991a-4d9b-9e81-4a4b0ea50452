import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../models/feature_item_model.dart';
import '../models/landing_model.dart';
import '../models/pricing_plan_model.dart';

part 'landing_event.dart';
part 'landing_state.dart';

class LandingBloc extends Bloc<LandingEvent, LandingState> {
  LandingBloc(super.initialState) {
    on<LandingInitialEvent>(_onInitialize);
    on<MenuButtonPressedEvent>(_onMenuButtonPressed);
    on<AppStoreButtonPressedEvent>(_onAppStoreButtonPressed);
    on<PlayStoreButtonPressedEvent>(_onPlayStoreButtonPressed);
    on<CtaAppStoreButtonPressedEvent>(_onCtaAppStoreButtonPressed);
    on<CtaPlayStoreButtonPressedEvent>(_onCtaPlayStoreButtonPressed);
    on<PricingPlanSelectedEvent>(_onPricingPlanSelected);
    on<EmailChangedEvent>(_onEmailChanged);
    on<SubscribeButtonPressedEvent>(_onSubscribeButtonPressed);
    on<BottomNavItemTappedEvent>(_onBottomNavItemTapped);
  }

  _onInitialize(LandingInitialEvent event, Emitter<LandingState> emit) async {
    final features = [
      FeatureItemModel(
        icon: ImageConstant.imgPiechart,
        title: 'Access to daily analytics',
        description:
            'Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps. Start training smarter, not harder.',
      ),
      FeatureItemModel(
        icon: ImageConstant.imgZap,
        title: 'Measure recovery',
        description:
            'The most advanced sleep tracking technology available today. Measure and track your recovery to unlock your greatest potential.',
      ),
      FeatureItemModel(
        icon: ImageConstant.imgSmartphone,
        title: 'Tech that evolves with you',
        description:
            'Know where your strengths lie and where you can improve. Untitled provides the latest tech with a steady stream of new features.',
      ),
      FeatureItemModel(
        icon: ImageConstant.imgUsers,
        title: 'Unrivalled community',
        description:
            'Join teams in the app with friends, family, and like-minded fitness enthusiasts. Create custom teams based on activities and interests.',
      ),
    ];

    final pricingPlans = [
      PricingPlanModel(
        price: '\$10/mth',
        title: 'Basic plan',
        description: 'Billed annually.',
        isPopular: true,
        features: [
          'Access to all basic features',
          'Basic reporting and analytics',
          'Up to 10 individual users',
          '20GB individual data each user',
          'Basic chat and email support',
        ],
      ),
      PricingPlanModel(
        price: '\$20/mth',
        title: 'Business plan',
        description: 'Billed annually.',
        isPopular: false,
        features: [
          '200+ integrations',
          'Advanced reporting',
          'Up to 20 individual users',
          '40GB individual data each user',
          'Priority chat and email support',
        ],
      ),
    ];

    emit(
      state.copyWith(
        emailController: TextEditingController(),
        landingModel: state.landingModel?.copyWith(features: features, pricingPlans: pricingPlans),
      ),
    );
  }

  _onMenuButtonPressed(MenuButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle menu button press
  }

  _onAppStoreButtonPressed(AppStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle App Store button press
  }

  _onPlayStoreButtonPressed(PlayStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle Play Store button press
  }

  _onCtaAppStoreButtonPressed(CtaAppStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle CTA App Store button press
  }

  _onCtaPlayStoreButtonPressed(CtaPlayStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle CTA Play Store button press
  }

  _onPricingPlanSelected(PricingPlanSelectedEvent event, Emitter<LandingState> emit) async {
    emit(state.copyWith(selectedPlan: event.plan));
  }

  _onEmailChanged(EmailChangedEvent event, Emitter<LandingState> emit) async {
    emit(state.copyWith(email: event.email));
  }

  _onSubscribeButtonPressed(SubscribeButtonPressedEvent event, Emitter<LandingState> emit) async {
    if (state.email?.isNotEmpty ?? false) {
      emit(state.copyWith(isSubscribed: true));
      state.emailController?.clear();
    }
  }

  _onBottomNavItemTapped(BottomNavItemTappedEvent event, Emitter<LandingState> emit) async {
    emit(state.copyWith(selectedNavItem: event.item));
  }
}
