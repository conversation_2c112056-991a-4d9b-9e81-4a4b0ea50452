import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import '../../l10n/app_localizations.dart';
import '../../widgets/custom_image_view.dart';
import '../localization/bloc/localization_bloc.dart';
import '../theme/bloc/theme_bloc.dart';
import './bloc/profile_bloc.dart';
import './models/profile_model.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<ProfileBloc>(
      create: (context) => ProfileBloc(ProfileState(profileModel: ProfileModel()))..add(ProfileInitialEvent()),
      child: const ProfileScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.white,
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              // color: Colors.white,
              borderRadius: BorderRadius.only(topRight: Radius.circular(16.h), bottomLeft: Radius.circular(16.h)),
            ),
            child: SingleChildScrollView(
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [_buildHeader(context), _buildProfileSection(context, state), _buildMenuItems(context)],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 4.h, left: 16.h, bottom: 32.h),
      child: GestureDetector(
        onTap: () {
          NavigatorService.goBack();
        },
        child: Row(
          children: [
            CustomImageView(imagePath: ImageConstant.imgArrowleft, height: 20.h, width: 20.h),
            SizedBox(width: 8.h),
            Opacity(
              opacity: 0.75,
              child: Text(
                AppLocalizations.of(context)?.back ?? 'Back',
                style: TextStyleHelper.instance.title16RegularPoppins.copyWith(color: appTheme.colorFF101828, height: 1.56),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, ProfileState state) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 70.h,
            height: 70.h,
            child: Stack(
              children: [
                CustomImageView(
                  imagePath: state.profileModel?.profileImage ?? ImageConstant.imgEllipse43,
                  height: 70.h,
                  width: 70.h,
                  fit: BoxFit.cover,
                  radius: BorderRadius.circular(35.h),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () {
                      context.read<ProfileBloc>().add(EditProfileImageEvent());
                    },
                    child: Container(
                      width: 20.h,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: appTheme.colorFFF9F5FF,
                        border: Border.all(color: appTheme.colorFF7E56, width: 1.h),
                        borderRadius: BorderRadius.circular(10.h),
                      ),
                      child: Center(
                        child: CustomImageView(imagePath: ImageConstant.imgEditImage, height: 12.h, width: 12.h),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16.h),
          Opacity(
            opacity: 0.75,
            child: Text(
              state.profileModel?.userName ?? 'John Doe',
              style: TextStyleHelper.instance.title18MediumPoppins.copyWith(color: appTheme.colorFF101828, height: 1.5),
            ),
          ),
          SizedBox(height: 4.h),
          Opacity(
            opacity: 0.75,
            child: Text(
              state.profileModel?.userEmail ?? '<EMAIL>',
              style: TextStyleHelper.instance.body12MediumPoppins.copyWith(color: appTheme.colorFF101828, height: 1.5),
            ),
          ),
          SizedBox(height: 32.h),
        ],
      ),
    );
  }

  Widget _buildMenuItems(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Column(
        children: [
          _buildDarkModeToggle(context),
          _buildDivider(),
          _buildLanguageSwitch(context),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgUser,
            title: AppLocalizations.of(context)?.editProfile ?? 'Edit Profile',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToEditProfileEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgSearchGray900_0c,
            title: AppLocalizations.of(context)?.address ?? 'Address',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToAddressEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgHistory,
            title: AppLocalizations.of(context)?.history ?? 'History',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToHistoryEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgComplain,
            title: AppLocalizations.of(context)?.complain ?? 'Complain',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToComplainEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgReferral,
            title: AppLocalizations.of(context)?.referral ?? 'Referral',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToReferralEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgAboutUs,
            title: AppLocalizations.of(context)?.aboutUs ?? 'About Us',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToAboutUsEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgSettings,
            title: AppLocalizations.of(context)?.settings ?? 'Settings',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToSettingsEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgHelpAndSupport,
            title: AppLocalizations.of(context)?.helpAndSupport ?? 'Help and Support',
            onTap: () {
              context.read<ProfileBloc>().add(NavigateToHelpSupportEvent());
            },
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            icon: ImageConstant.imgLogout,
            title: AppLocalizations.of(context)?.logout ?? 'Logout',
            isLogout: true,
            onTap: () {
              context.read<ProfileBloc>().add(LogoutEvent());
            },
          ),
          SizedBox(height: 32.h),
        ],
      ),
    );
  }

  Widget _buildDarkModeToggle(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          child: Row(
            children: [
              CustomImageView(imagePath: ImageConstant.darkMode, height: 20.h, width: 20.h),
              SizedBox(width: 8.h),
              Expanded(
                child: Opacity(
                  opacity: 0.75,
                  child: Text(
                    AppLocalizations.of(context)?.darkMode ?? 'Dark Mode',
                    style: TextStyleHelper.instance.body12MediumInter.copyWith(color: appTheme.colorFF101828, height: 1.25),
                  ),
                ),
              ),
              Switch(
                value: themeState.isDarkMode,
                onChanged: (value) {
                  context.read<ThemeBloc>().add(ToggleThemeEvent());
                },
                activeColor: appTheme.colorFF7E56,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageSwitch(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, localizationState) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          child: Row(
            children: [
              CustomImageView(imagePath: ImageConstant.imgHistory, height: 20.h, width: 2.h),
              SizedBox(width: 8.h),
              Expanded(
                child: Opacity(
                  opacity: 0.75,
                  child: Text(
                    AppLocalizations.of(context)?.language ?? 'Language',
                    style: TextStyleHelper.instance.body12MediumInter.copyWith(color: appTheme.colorFF101828, height: 1.25),
                  ),
                ),
              ),
              _buildModernLanguageToggle(context, localizationState),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModernLanguageToggle(BuildContext context, LocalizationState localizationState) {
    final isEnglish = localizationState.locale.languageCode == 'en';

    return Container(
      decoration: BoxDecoration(
        color: appTheme.colorFFF9F5FF,
        borderRadius: BorderRadius.circular(12.h),
        border: Border.all(color: appTheme.colorFF7E56.withValues(alpha: 0.2), width: 1.h),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildLanguageOption(
            context: context,
            label: 'EN',
            fullLabel: AppLocalizations.of(context)?.english ?? 'English',
            isSelected: isEnglish,
            onTap: () => _changeLanguage(context, 'en'),
            isFirst: true,
          ),
          _buildLanguageOption(
            context: context,
            label: 'عر',
            fullLabel: AppLocalizations.of(context)?.arabic ?? 'العربية',
            isSelected: !isEnglish,
            onTap: () => _changeLanguage(context, 'ar'),
            isFirst: false,
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption({
    required BuildContext context,
    required String label,
    required String fullLabel,
    required bool isSelected,
    required VoidCallback onTap,
    required bool isFirst,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? appTheme.colorFF7E56 : Colors.transparent,
          borderRadius: BorderRadius.circular(10.h),
          boxShadow: isSelected ? [BoxShadow(color: appTheme.colorFF7E56.withValues(alpha: 0.3), blurRadius: 4.h, offset: Offset(0, 2.h))] : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyleHelper.instance.body12MediumInter.copyWith(
                color: isSelected ? appTheme.whiteCustom : appTheme.colorFF101828,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            if (isSelected) ...[SizedBox(width: 4.h), Icon(Icons.check_circle, size: 14.h, color: appTheme.whiteCustom)],
          ],
        ),
      ),
    );
  }

  void _changeLanguage(BuildContext context, String languageCode) {
    context.read<LocalizationBloc>().add(ChangeLanguageEvent(Locale(languageCode)));
  }

  Widget _buildDivider() {
    return IntrinsicHeight(
      child: OverflowBox(
        maxWidth: 238.h,
        child: Container(
          height: 1.h,
          color: appTheme.colorFFE5E7EB,
          margin: EdgeInsets.symmetric(vertical: 12.h),
        ),
      ),
    );
  }

  Widget _buildMenuItem(BuildContext context, {required String icon, required String title, required VoidCallback onTap, bool isLogout = false}) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          children: [
            CustomImageView(imagePath: icon, height: 20.h, width: 20.h),
            SizedBox(width: 8.h),
            Opacity(
              opacity: 0.75,
              child: Text(
                title,
                style: TextStyleHelper.instance.body12MediumInter.copyWith(
                  color: isLogout ? appTheme.colorFF5252 : appTheme.colorFF101828,
                  height: 1.25,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
